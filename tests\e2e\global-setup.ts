/* eslint-disable no-console */
import { chromium, webkit, FullConfig } from '@playwright/test'
import { exec } from 'child_process'
import { promisify } from 'util'

const execAsync = promisify(exec)

async function cleanupWebKitProcesses() {
  console.log('🧹 Cleaning up WebKit processes...')

  const commands = [
    'pkill -9 safaridriver || true',
    'pkill -9 WebKitWebContent || true',
    'pkill -9 WebKitNetworkProcess || true',
    'pkill -9 WebKitWebProcess || true',
    'pkill -9 "com.apple.WebKit.WebContent" || true',
    'pkill -9 "Safari" || true',
    'pkill -9 "WebKit" || true'
  ]

  // Execute all commands in parallel for efficiency
  await Promise.all(
    commands.map(async (command) => {
      try {
        await execAsync(command)
      } catch (error) {
        // Ignore errors - processes might not exist
      }
    })
  )

  console.log('✅ WebKit process cleanup completed')
}

async function globalSetup(config: FullConfig) {
  console.log('🔧 Global setup: Starting browser context validation...')

  // Clean up any existing WebKit processes first
  if (process.platform === 'darwin') {
    await cleanupWebKitProcesses()
  }

  // Test Chromium browser context creation
  console.log('🔧 Testing Chromium browser context...')
  const chromiumBrowser = await chromium.launch()
  const chromiumContext = await chromiumBrowser.newContext()
  const chromiumPage = await chromiumContext.newPage()

  try {
    await chromiumPage.goto('data:text/html,<html><body>Chromium Test</body></html>')
    console.log('✅ Chromium browser context test passed')
  } catch (error) {
    console.error('❌ Chromium browser context test failed:', error)
    throw error
  } finally {
    await chromiumContext.close()
    await chromiumBrowser.close()
  }

  // Test WebKit browser context creation with retries
  console.log('🔧 Testing WebKit browser context...')

  const testWebKitWithRetries = async (): Promise<boolean> => {
    const maxAttempts = 3

    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      console.log(`🔧 WebKit attempt ${attempt}/${maxAttempts}...`)

      try {
        const webkitBrowser = await webkit.launch({
          timeout: 60000,
          args: [
            '--disable-web-security',
            '--disable-features=TranslateUI',
            '--disable-ipc-flooding-protection',
          ]
        })
        const webkitContext = await webkitBrowser.newContext()
        const webkitPage = await webkitContext.newPage()

        await webkitPage.goto('data:text/html,<html><body>WebKit Test</body></html>', {
          timeout: 30000
        })

        await webkitContext.close()
        await webkitBrowser.close()

        console.log('✅ WebKit browser context test passed')
        return true
      } catch (error) {
        console.error(`❌ WebKit browser context test failed (attempt ${attempt}):`, error)

        if (attempt < maxAttempts) {
          console.log('🔄 Retrying WebKit test after cleanup...')
          if (process.platform === 'darwin') {
            await cleanupWebKitProcesses()
          }
          await new Promise(resolve => setTimeout(resolve, 2000)) // Wait 2 seconds
        }
      }
    }

    console.warn('⚠️ WebKit browser context test failed after all attempts. Tests may be unstable.')
    return false
  }

  await testWebKitWithRetries()

  // Log resource information
  console.log('📊 System resources at test start:')
  console.log(`- Workers: ${config.workers}`)
  console.log(`- Projects: ${config.projects.length}`)
  console.log(`- Version: ${config.version}`)
  console.log(`- Platform: ${process.platform}`)
  console.log(`- Node version: ${process.version}`)

  const memUsage = process.memoryUsage()
  console.log(`- Memory heap used: ${Math.round(memUsage.heapUsed / 1024 / 1024)}MB`)
  console.log(`- Memory heap total: ${Math.round(memUsage.heapTotal / 1024 / 1024)}MB`)
  console.log(`- Memory RSS: ${Math.round(memUsage.rss / 1024 / 1024)}MB`)

  return async () => {
    console.log('🧹 Global setup cleanup completed')
  }
}

export default globalSetup
