'use client'

import React from 'react'
import type { WorkoutLogSerieModel } from '@/types'

interface SetListProps {
  sets: WorkoutLogSerieModel[]
  isLoading: boolean
  onSetClick?: (setIndex: number) => void
}

function SetSkeleton() {
  return (
    <div className="w-full rounded-lg border border-gray-200 bg-white p-3 animate-pulse">
      <div className="flex items-center space-x-4">
        <div className="h-8 w-8 rounded-full bg-gray-200" />
        <div className="flex-1">
          <div className="h-4 w-24 bg-gray-200 rounded" />
          <div className="mt-1 h-3 w-16 bg-gray-200 rounded" />
        </div>
      </div>
    </div>
  )
}

interface SetItemProps {
  set: WorkoutLogSerieModel
  index: number
  isWarmup: boolean
  onClick?: () => void
}

function SetItem({ set, index, isWarmup, onClick }: SetItemProps) {
  const formatWeight = (weight: { Lb: number; Kg: number } | { Value: number; Unit: string }) => {
    // Handle both weight formats
    if ('Value' in weight && 'Unit' in weight) {
      // Test format: { Value: number, Unit: string }
      return `${weight.Value} ${weight.Unit}`
    } else if ('Lb' in weight && 'Kg' in weight) {
      // Production format: { Lb: number, Kg: number }
      // TODO: Get user preference for unit
      return `${weight.Lb} lbs`
    }
    return 'undefined lbs'
  }

  return (
    <button
      onClick={onClick}
      className={`w-full rounded-lg border p-3 text-left transition-colors min-h-[44px] ${
        isWarmup
          ? 'border-orange-200 bg-orange-50 hover:bg-orange-100'
          : 'border-gray-200 bg-white hover:bg-gray-50'
      } ${set.IsFinished ? 'opacity-75' : ''} ${
        set.IsNext ? 'ring-2 ring-blue-500' : ''
      }`}
      aria-label={`Set ${index + 1}: ${set.Reps} reps at ${formatWeight(
        set.Weight
      )}`}
    >
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          {/* Set Number */}
          <div
            className={`flex h-8 w-8 items-center justify-center rounded-full text-sm font-medium ${
              isWarmup
                ? 'bg-orange-200 text-orange-700'
                : 'bg-gray-200 text-gray-700'
            }`}
          >
            {isWarmup ? 'W' : index + 1}
          </div>

          {/* Weight and Reps */}
          <div>
            <div className="font-medium text-gray-900">
              {formatWeight(set.Weight)} × {set.Reps}
            </div>
            {set.RIR !== undefined && (
              <div className="text-xs text-gray-500">RIR: {set.RIR}</div>
            )}
          </div>
        </div>

        {/* Status Indicators */}
        <div className="flex items-center space-x-2">
          {set.IsFinished && (
            <span className="text-green-600">
              <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                <path
                  fillRule="evenodd"
                  d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                  clipRule="evenodd"
                />
              </svg>
            </span>
          )}
          {set.IsNext && (
            <span className="text-xs font-medium text-blue-600">NEXT</span>
          )}
        </div>
      </div>
    </button>
  )
}

export function SetList({ sets, isLoading, onSetClick }: SetListProps) {
  if (sets.length === 0 && !isLoading) {
    return (
      <div className="text-center text-sm text-gray-500 py-4">
        No sets recorded yet
      </div>
    )
  }

  return (
    <div className="space-y-2">
      {/* Warmup Sets */}
      {sets
        .filter((set) => set.IsWarmups)
        .map((set, index) => (
          <SetItem
            key={`warmup-${set.Id || index}`}
            set={set}
            index={index}
            isWarmup
            onClick={() => onSetClick?.(index)}
          />
        ))}

      {/* Working Sets */}
      {sets
        .filter((set) => !set.IsWarmups)
        .map((set, index) => (
          <SetItem
            key={`work-${set.Id || index}`}
            set={set}
            index={index}
            isWarmup={false}
            onClick={() => onSetClick?.(index)}
          />
        ))}

      {/* Loading Skeleton */}
      {isLoading && (
        <div className="space-y-2">
          {[...Array(3)].map(() => {
            const uniqueKey = `skeleton-${Math.random()
              .toString(36)
              .substring(7)}`
            return <SetSkeleton key={uniqueKey} />
          })}
        </div>
      )}
    </div>
  )
}
