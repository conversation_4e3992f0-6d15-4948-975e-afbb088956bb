Step-by-Step Review Plan for Exercise Set Generation

1. Review the Test Suite (src/utils/**tests**/createWorkoutSets.test.ts)
   Verify all test cases are still passing
   Check coverage for:
   ✓ Warm-up set generation
   ✓ Normal work sets
   ✓ Pyramid sets (weight ↑, reps ↓)
   ✓ Reverse pyramid sets (weight ↓, reps ↑)
   ✓ Back-off sets (20% lighter after main set)
   ✓ Rest-pause sets (single set with pauses)
   ✓ Weight unit formatting (kg/lbs)
   ✓ Exercise properties propagation
2. Verify Core Implementation Files
   src/utils/createWorkoutSets.ts - Main function
   src/utils/workoutSetGenerators.ts - Set generation logic
   src/utils/workoutSetGenerators2.ts - Additional generators
   src/utils/workoutSetHelpers.ts - Utility functions
3. Check Mobile App Logic Compliance
   Compare against the original C# implementation:

csharp

// Key aspects from KenkoSingleExercisePage.xaml.cs:

- SetNo: "W" for warmups, "1", "2", etc. for work sets
- IsNext: First unfinished set
- SetTitle: Context-specific titles
- LastTimeSet: "Last time: X x Y kg/lbs"
- Weight calculations: 10% increase/decrease
- Reps adjustments: ±2 reps for pyramid/back-off

4. Verify Type Definitions
   WorkoutLogSerieModelRef interface includes all required fields:
   Base fields from WorkoutLogSerieModel
   Additional UI fields (SetTitle, LastTimeSet, etc.)
   Exercise properties (IsBodyweight, IsTimeBased, etc.)
5. Integration Points to Check
   Is createWorkoutSets being called in the exercise page?
   Are recommendations being passed correctly?
   Is the weight unit preference (kg/lbs) being respected?
   Are the generated sets being displayed in the UI?
6. Common Breakage Points
   Recommendation Loading: Ensure recommendations have all required fields
   Weight Calculations: Check rounding and increment logic
   State Management: Verify sets are stored/retrieved correctly
   UI Binding: Ensure SetScreen/ExerciseSetsGrid uses the generated sets
7. Debug Checklist
   typescript

// Check these values when debugging:

1. recommendation.Series (number of sets)
2. recommendation.IsPyramid/IsReversePyramid/IsBackOffSet
3. recommendation.WarmUpsList (array of warmups)
4. recommendation.FirstWorkSetWeight/FirstWorkSetReps
5. recommendation.Increments (weight increments)
6. exercise.IsBodyweight/IsTimeBased/etc.
7. Testing Approach
   Run unit tests: npm run test:unit -- src/utils/**tests**/createWorkoutSets.test.ts
   Check TypeScript: npm run typecheck
   Verify lint: npm run lint
   Test with mock data in the UI
   Test with real API recommendations
8. Expected Output Structure
   typescript

// Example output for pyramid sets:
[
{ SetNo: "W", IsWarmups: true, Weight: 40kg, Reps: 5 },
{ SetNo: "W", IsWarmups: true, Weight: 50kg, Reps: 3 },
{ SetNo: "1", Weight: 60kg, Reps: 10, SetTitle: "Pyramid set:" },
{ SetNo: "2", Weight: 66kg, Reps: 8 },
{ SetNo: "3", Weight: 72.5kg, Reps: 6 }
] 10. Validation Points
All sets have required fields populated
Weight progression follows 10% rule
Reps adjust by 2 for pyramid/reverse pyramid
Back-off sets are 20% lighter (or use BackOffSetWeight)
Rest-pause sets have NbPause field
First work set shows "Last time" info
