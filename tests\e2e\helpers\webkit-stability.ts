/* eslint-disable no-console */
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ontext, Page, test } from '@playwright/test'

/**
 * WebKit stability helper for handling browser crashes and context issues
 */
export class WebKitStabilityHelper {
  private static readonly MAX_RETRIES = 3
  private static readonly RETRY_DELAY = 2000

  /**
   * Create a new browser context with retry logic for WebKit stability
   */
  static async createStableContext(browser: Browser): Promise<BrowserContext> {
    const createContextWithRetry = async (): Promise<BrowserContext> => {
      let lastError: Error | null = null

      for (let attempt = 1; attempt <= this.MAX_RETRIES; attempt++) {
        try {
          console.log(`🔧 Creating browser context (attempt ${attempt}/${this.MAX_RETRIES})`)

          const context = await browser.newContext({
            viewport: { width: 390, height: 844 },
            reducedMotion: 'reduce',
            bypassCSP: true,
            ignoreHTTPSErrors: true,
          })

          console.log('✅ Browser context created successfully')
          return context
        } catch (error) {
          lastError = error as Error
          console.error(`❌ Browser context creation failed (attempt ${attempt}):`, error)

          if (attempt < this.MAX_RETRIES) {
            console.log(`🔄 Retrying in ${this.RETRY_DELAY}ms...`)
            await new Promise(resolve => setTimeout(resolve, this.RETRY_DELAY))
          }
        }
      }

      throw new Error(`Failed to create browser context after ${this.MAX_RETRIES} attempts. Last error: ${lastError?.message}`)
    }

    return createContextWithRetry()
  }

  /**
   * Create a new page with retry logic for WebKit stability
   */
  static async createStablePage(context: BrowserContext): Promise<Page> {
    const createPageWithRetry = async (): Promise<Page> => {
      let lastError: Error | null = null

      for (let attempt = 1; attempt <= this.MAX_RETRIES; attempt++) {
        try {
          console.log(`🔧 Creating page (attempt ${attempt}/${this.MAX_RETRIES})`)

          const page = await context.newPage()

          // Add error handling for page crashes
          page.on('crash', () => {
            console.error('💥 Page crashed!')
          })

          page.on('pageerror', (error) => {
            console.error('🚨 Page error:', error)
          })

          console.log('✅ Page created successfully')
          return page
        } catch (error) {
          lastError = error as Error
          console.error(`❌ Page creation failed (attempt ${attempt}):`, error)

          if (attempt < this.MAX_RETRIES) {
            console.log(`🔄 Retrying in ${this.RETRY_DELAY}ms...`)
            await new Promise(resolve => setTimeout(resolve, this.RETRY_DELAY))
          }
        }
      }

      throw new Error(`Failed to create page after ${this.MAX_RETRIES} attempts. Last error: ${lastError?.message}`)
    }

    return createPageWithRetry()
  }

  /**
   * Navigate to a URL with retry logic for WebKit stability
   */
  static async navigateStable(page: Page, url: string): Promise<void> {
    const navigateWithRetry = async (): Promise<void> => {
      let lastError: Error | null = null

      for (let attempt = 1; attempt <= this.MAX_RETRIES; attempt++) {
        try {
          console.log(`🔧 Navigating to ${url} (attempt ${attempt}/${this.MAX_RETRIES})`)

          await page.goto(url, {
            timeout: 45000,
            waitUntil: 'domcontentloaded'
          })

          console.log('✅ Navigation successful')
          return
        } catch (error) {
          lastError = error as Error
          console.error(`❌ Navigation failed (attempt ${attempt}):`, error)

          if (attempt < this.MAX_RETRIES) {
            console.log(`🔄 Retrying navigation in ${this.RETRY_DELAY}ms...`)
            await new Promise(resolve => setTimeout(resolve, this.RETRY_DELAY))
          }
        }
      }

      throw new Error(`Failed to navigate to ${url} after ${this.MAX_RETRIES} attempts. Last error: ${lastError?.message}`)
    }

    await navigateWithRetry()
  }

  /**
   * Check if an error is related to browser context closure
   */
  static isBrowserContextError(error: Error): boolean {
    const errorMessage = error.message.toLowerCase()
    return (
      errorMessage.includes('browser has been closed') ||
      errorMessage.includes('context has been closed') ||
      errorMessage.includes('target page, context or browser has been closed') ||
      errorMessage.includes('browsercontext.newpage') ||
      errorMessage.includes('protocol error')
    )
  }

  /**
   * Playwright test wrapper with WebKit stability handling
   */
  static withStability(testName: string, testFn: (page: Page) => Promise<void>) {
    return test(testName, async ({ browser }) => {
      let context: BrowserContext | null = null
      let page: Page | null = null
      
      try {
        context = await this.createStableContext(browser)
        page = await this.createStablePage(context)
        
        await testFn(page)
      } catch (error) {
        if (this.isBrowserContextError(error as Error)) {
          console.error('🚨 Browser context error detected:', error)
          throw new Error(`WebKit browser context failure: ${(error as Error).message}`)
        }
        throw error
      } finally {
        try {
          if (page) {
            await page.close()
          }
          if (context) {
            await context.close()
          }
        } catch (cleanupError) {
          console.warn('⚠️ Cleanup error (non-fatal):', cleanupError)
        }
      }
    })
  }
}

/**
 * Test hook for WebKit stability - use in beforeEach
 */
export async function setupWebKitStability() {
  // Add any global WebKit stability setup here
  console.log('🔧 Setting up WebKit stability measures')
}

/**
 * Test hook for WebKit cleanup - use in afterEach
 */
export async function cleanupWebKitStability() {
  // Add any global WebKit cleanup here
  console.log('🧹 Cleaning up WebKit stability measures')
}
