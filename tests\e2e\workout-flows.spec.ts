/* eslint-disable no-console */
import { test, expect } from '@playwright/test'
import { setupRobustTestCleanup, setupPageWithRetry, waitForElementSafely, clickSafely } from './helpers/robust-test-setup'
import { login, waitForLoadingComplete, clickFirstExercise } from './helpers'
import { createConsoleErrorMonitor } from './helpers/critical-flow-utils'

// Mobile-first configuration
test.use({
  viewport: { width: 375, height: 667 }, // iPhone SE
  hasTouch: true,
  isMobile: true,
})

test.describe('@critical Workout Flows - Creation and Navigation', () => {
  // No mocks - using real API with test credentials

  // Use robust cleanup for all tests in this suite
  setupRobustTestCleanup()

  test('Start workout flow - create and navigate without race conditions', async ({
    page,
    browserName,
  }) => {
    // Set longer timeout for Safari
    if (browserName === 'webkit') {
      test.setTimeout(60000)
    }
    // First login with real credentials
    await page.goto('/login')
    await login(page, '<EMAIL>', 'Dr123456')

    // Wait for navigation to program page
    await page.waitForURL('/program', { timeout: 10000 })
    await waitForLoadingComplete(page)

    // Look for the start workout button - text is "Open Workout"
    const startWorkoutButton = page
      .getByRole('button', {
        name: /open workout/i,
      })
      .first()

    // Wait for the button to be visible
    await expect(startWorkoutButton).toBeVisible({ timeout: 10000 })

    // Monitor for race conditions during workout creation
    const errorMonitor = createConsoleErrorMonitor(page)

    await startWorkoutButton.click()

    // Verify navigation to workout page
    await expect(page).toHaveURL('/workout', { timeout: 10000 })

    // Check for race condition errors
    expect(errorMonitor.getRaceConditionErrors()).toHaveLength(0)
  })

  test('Open exercise flow - navigate to exercise page', async ({
    page,
    browserName,
  }) => {
    // Set longer timeout for Safari
    if (browserName === 'webkit') {
      test.setTimeout(60000)
    }
    // First login and start a workout
    await page.goto('/login')
    await login(page, '<EMAIL>', 'Dr123456')
    await page.waitForURL('/program', { timeout: 10000 })
    await waitForLoadingComplete(page)

    // Open workout
    const workoutButton = page
      .getByRole('button', {
        name: /open workout/i,
      })
      .first()
    await expect(workoutButton).toBeVisible({ timeout: 10000 })
    await workoutButton.click()

    // Wait for navigation to workout page
    await page.waitForURL('/workout', { timeout: 10000 })
    await waitForLoadingComplete(page)

    // Monitor console for errors
    const errorMonitor = createConsoleErrorMonitor(page)

    // Wait for exercises to load
    await page.waitForTimeout(3000)

    // Try to find and click exercise element
    const clicked = await clickFirstExercise(page)

    if (!clicked) {
      // Take a screenshot for debugging
      await page.screenshot({ path: 'debug-workout-page.png' })
      throw new Error('No exercise elements found on workout page')
    }

    // Wait for navigation to exercise page
    await page.waitForURL(/\/exercise\/\d+/, { timeout: 10000 })

    // Verify we're on exercise page
    await expect(page).toHaveURL(/\/exercise\/\d+/)

    // Verify no race condition errors
    expect(errorMonitor.getRaceConditionErrors()).toHaveLength(0)
  })
})
