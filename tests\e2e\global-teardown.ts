/* eslint-disable no-console, @typescript-eslint/no-unused-vars */
import { FullConfig } from '@playwright/test'
import { exec } from 'child_process'
import { promisify } from 'util'

const execAsync = promisify(exec)

async function cleanupWebKitProcesses() {
  console.log('🧹 [TEARDOWN] Cleaning up WebKit processes...')

  const commands = [
    'pkill -9 safaridriver || true',
    'pkill -9 WebKitWebContent || true',
    'pkill -9 WebKitNetworkProcess || true',
    'pkill -9 WebKitWebProcess || true',
    'pkill -9 "com.apple.WebKit.WebContent" || true',
    'pkill -9 "Safari" || true',
    'pkill -9 "WebKit" || true'
  ]

  // Execute all commands in parallel for efficiency
  await Promise.all(
    commands.map(async (command) => {
      try {
        await execAsync(command)
      } catch (error) {
        // Ignore errors - processes might not exist
      }
    })
  )

  console.log('✅ [TEARDOWN] WebKit process cleanup completed')
}

/**
 * Global teardown for Playwright tests
 * Runs once after all tests complete
 */
async function globalTeardown(_config: FullConfig) {
  console.log('🧹 [TEARDOWN] Starting global teardown...')

  // Clean up WebKit processes on macOS
  if (process.platform === 'darwin') {
    await cleanupWebKitProcesses()
  }

  // Log final resource state
  const memUsage = process.memoryUsage()
  console.log('📊 Final system resources:')
  console.log(
    `- Memory heap used: ${Math.round(memUsage.heapUsed / 1024 / 1024)}MB`
  )
  console.log(
    `- Memory heap total: ${Math.round(memUsage.heapTotal / 1024 / 1024)}MB`
  )
  console.log(`- Memory RSS: ${Math.round(memUsage.rss / 1024 / 1024)}MB`)

  // Additional cleanup - force garbage collection if available
  if (global.gc) {
    global.gc()
    console.log('🗑️ [TEARDOWN] Forced garbage collection')
  }

  // Log completion
  console.log('✅ [TEARDOWN] Global teardown completed')
}

export default globalTeardown
