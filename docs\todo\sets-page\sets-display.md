# Sets Display Architecture

## Overview

The Dr. Muscle X app displays workout sets in a comprehensive manner, showing both current workout recommendations and historical data from previous workouts.

## Key Components

### 1. SetDisplay Component

- **Location**: `/src/components/workout/SetDisplay.tsx`
- **Purpose**: Displays individual set with reps, weight, and status
- **Features**:
  - Visual states: active, finished, warmup
  - Set types: Rest-pause, Back-off, Drop set
  - Touch-optimized with 44px minimum targets

### 2. AllSetsDisplay Component

- **Location**: `/src/components/workout/AllSetsDisplay.tsx`
- **Purpose**: Container showing all sets for an exercise
- **Data Sources**:
  - `recommendation.Series`: Number of work sets from API
  - `recommendation.WarmupsCount`: Number of warmup sets
  - `recommendation.HistorySet`: Previous workout data
- **Features**:
  - Displays historical sets from previous workout
  - Shows all current sets (warmups + work sets)
  - Progress tracking with completion percentage
  - Click/tap navigation between sets

### 3. HistoricalSetsDisplay Component

- **Location**: `/src/components/workout/HistoricalSetsDisplay.tsx`
- **Purpose**: Shows previous workout sets
- **Data**: Uses `recommendation.HistorySet` array from API

## Data Flow

```
API Response (RecommendationModel)
├── Series (number of work sets)
├── WarmupsCount (number of warmup sets)
├── HistorySet (previous workout data)
└── Weight, Reps, etc.
    ↓
useSetScreenLogic Hook
├── totalSets = recommendation.Series || 4
└── Manages current set index
    ↓
SetScreen Component
└── AllSetsDisplay
    ├── HistoricalSetsDisplay (if HistorySet exists)
    └── Current Sets Display
        ├── Warmup Sets (0 to WarmupsCount)
        └── Work Sets (1 to Series)
```

## API Integration

The `RecommendationModel` interface includes:

```typescript
{
  Series: number,              // Number of work sets
  WarmupsCount: number,        // Number of warmup sets
  HistorySet: WorkoutLogSerieModel[], // Previous workout data
  // ... other recommendation data
}
```

## Display Logic

1. **Total Sets Calculation**:
   - Warmup sets: 0 to `recommendation.WarmupsCount`
   - Work sets: 1 to `recommendation.Series`
   - Total displayed: `WarmupsCount + Series`

2. **Set Numbering**:
   - Warmups labeled as "Warm-up"
   - Work sets numbered 1 to n

3. **Historical Data**:
   - Shown above current sets if available
   - Displays reps and weight from previous workout

## Mobile Optimization

- Touch targets minimum 44px height
- Clear visual states for active/completed sets
- Smooth transitions and haptic feedback
- Progress bar for quick visual reference
