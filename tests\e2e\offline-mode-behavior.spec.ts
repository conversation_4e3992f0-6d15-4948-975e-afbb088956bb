import { test, expect } from '@playwright/test'
import { setupRobustTestCleanup, setupPageWithRetry, waitForElementSafely, clickSafely } from './helpers/robust-test-setup'

test.describe('Offline Mode Behavior @critical', () => {
  const TEST_USER = {
    email: '<EMAIL>',
    password: 'Dr123456'
  }

  // Use robust cleanup for all tests in this suite
  setupRobustTestCleanup()

  test.beforeEach(async ({ page }) => {
    // Login before each test with robust error handling
    try {
      await setupPageWithRetry(page, '/login')

      await waitForElementSafely(page, 'input[name="email"], [data-testid="email-input"]')
      await page.getByLabel('Email').fill(TEST_USER.email)
      await page.getByLabel('Password').fill(TEST_USER.password)

      await clickSafely(page, 'button[type="submit"], [data-testid="login-button"]')
      await page.waitForURL('/program', { timeout: 30000 })

      console.log('✅ [OFFLINE-MODE-BEFOREEACH] Login completed successfully')
    } catch (error) {
      console.error('❌ [OFFLINE-MODE-BEFOREEACH] Login failed:', error)
      throw error
    }
  })

  test('should work offline after initial load @critical', async ({ page, context }) => {
    // Navigate to workout while online
    await page.getByRole('button', { name: /start workout|today's workout/i }).click()
    await page.waitForURL('/workout')
    
    // Wait for workout data to load
    await expect(page.locator('[data-testid="exercise-list"]')).toBeVisible()
    
    // Go offline
    await context.setOffline(true)
    
    // Should still be able to navigate between exercises
    const firstExercise = page.locator('[data-testid="exercise-item"]').first()
    await firstExercise.click()
    
    // Exercise detail should load from cache
    await expect(page.locator('[data-testid="set-input"]')).toBeVisible()
    
    // Should be able to input data
    await page.locator('input[name="weight"]').fill('100')
    await page.locator('input[name="reps"]').fill('10')
    
    // Save should work (queued)
    await page.getByRole('button', { name: /save|next|done/i }).click()
    
    // Should show offline indicator
    const offlineIndicator = page.locator('[data-testid="offline-indicator"]')
    await expect(offlineIndicator).toBeVisible()
    
    // Go back online
    await context.setOffline(false)
  })

  test('should queue workout data when offline @critical', async ({ page, context }) => {
    // Navigate to workout
    await page.getByRole('button', { name: /start workout|today's workout/i }).click()
    await page.waitForURL('/workout')
    
    // Go offline
    await context.setOffline(true)
    
    // Complete multiple sets while offline
    const setsToComplete = 3
    const queuedData = []
    
    for (let i = 0; i < setsToComplete; i++) {
      await page.locator('[data-testid="exercise-item"]').first().click()
      
      const weight = `${100 + i * 10}`
      const reps = `${10 - i}`
      queuedData.push({ weight, reps })
      
      await page.locator('input[name="weight"]').fill(weight)
      await page.locator('input[name="reps"]').fill(reps)
      await page.getByRole('button', { name: /save|next|done/i }).click()
      
      // Should show queued indicator
      const queueIndicator = page.locator('[data-testid="sync-queued"]')
      await expect(queueIndicator).toBeVisible()
      
      // Go back to exercise list
      const backButton = page.locator('[data-testid="back-button"]')
      if (await backButton.isVisible()) {
        await backButton.click()
      }
    }
    
    // Check queue count
    const queueCount = page.locator('[data-testid="queue-count"]')
    if (await queueCount.isVisible()) {
      await expect(queueCount).toContainText(String(setsToComplete))
    }
    
    // Monitor for sync when going online
    let syncCount = 0
    page.on('response', response => {
      if (response.url().includes('/SaveWorkoutLog') && response.status() === 200) {
        syncCount++
      }
    })
    
    // Go back online
    await context.setOffline(false)
    
    // Wait for queue to process
    await page.waitForTimeout(5000)
    
    // Verify all items were synced
    expect(syncCount).toBeGreaterThanOrEqual(setsToComplete)
    
    // Queue indicator should disappear
    await expect(queueCount).not.toBeVisible()
  })

  test('should show offline UI indicators @critical', async ({ page, context }) => {
    // Go offline
    await context.setOffline(true)
    
    // Reload page while offline
    await page.reload()
    
    // Should show offline banner or indicator
    const offlineBanner = page.locator('[data-testid="offline-banner"]')
    const offlineIcon = page.locator('[data-testid="offline-icon"]')
    await expect(offlineBanner.or(offlineIcon)).toBeVisible()
    
    // Navigation should still work
    await expect(page.locator('nav')).toBeVisible()
    
    // Go back online
    await context.setOffline(false)
    
    // Offline indicators should disappear
    await expect(offlineBanner.or(offlineIcon)).not.toBeVisible({ timeout: 5000 })
  })

  test('should handle offline login attempt @critical', async ({ page, context }) => {
    // Logout first
    await page.locator('[data-testid="user-avatar"]').click()
    await page.getByRole('button', { name: /log out|sign out/i }).click()
    await page.waitForURL('/login')
    
    // Go offline
    await context.setOffline(true)
    
    // Try to login
    await page.getByLabel('Email').fill(TEST_USER.email)
    await page.getByLabel('Password').fill(TEST_USER.password)
    await page.getByRole('button', { name: /log in/i }).click()
    
    // Should show offline error
    await expect(page.locator('[role="alert"]')).toBeVisible()
    await expect(page.locator('[role="alert"]')).toContainText(/offline|network|connection/i)
    
    // Go back online
    await context.setOffline(false)
  })

  test('should cache essential workout data @critical', async ({ page, context }) => {
    // Load workout data while online
    await page.getByRole('button', { name: /start workout|today's workout/i }).click()
    await page.waitForURL('/workout')
    
    // Wait for all exercises to load
    await expect(page.locator('[data-testid="exercise-list"]')).toBeVisible()
    const exerciseCount = await page.locator('[data-testid="exercise-item"]').count()
    
    // Go offline
    await context.setOffline(true)
    
    // Refresh page
    await page.reload()
    
    // Workout data should still be available
    await expect(page.locator('[data-testid="exercise-list"]')).toBeVisible()
    const offlineExerciseCount = await page.locator('[data-testid="exercise-item"]').count()
    
    // Should have same number of exercises
    expect(offlineExerciseCount).toBe(exerciseCount)
    
    // Exercise details should be accessible
    await page.locator('[data-testid="exercise-item"]').first().click()
    await expect(page.locator('[data-testid="exercise-name"]')).toBeVisible()
    
    // Go back online
    await context.setOffline(false)
  })

  test('should handle offline navigation @critical', async ({ page, context }) => {
    // Pre-load multiple pages while online
    await page.goto('/program')
    await page.goto('/workout')
    await page.goto('/program')
    
    // Go offline
    await context.setOffline(true)
    
    // Should be able to navigate between cached pages
    await page.goto('/workout')
    await expect(page).toHaveURL('/workout')
    await expect(page.locator('main')).toBeVisible()
    
    await page.goto('/program')
    await expect(page).toHaveURL('/program')
    await expect(page.locator('main')).toBeVisible()
    
    // Go back online
    await context.setOffline(false)
  })

  test('should preserve form data during offline/online transitions @critical', async ({ page, context }) => {
    // Navigate to exercise
    await page.getByRole('button', { name: /start workout|today's workout/i }).click()
    await page.waitForURL('/workout')
    await page.locator('[data-testid="exercise-item"]').first().click()
    
    // Start filling form
    await page.locator('input[name="weight"]').fill('150')
    await page.locator('input[name="reps"]').fill('8')
    
    // Go offline
    await context.setOffline(true)
    
    // Data should still be in form
    await expect(page.locator('input[name="weight"]')).toHaveValue('150')
    await expect(page.locator('input[name="reps"]')).toHaveValue('8')
    
    // Try to save
    await page.getByRole('button', { name: /save|next|done/i }).click()
    
    // Should show queued message
    await expect(page.locator('[data-testid="sync-queued"]')).toBeVisible()
    
    // Go back online
    await context.setOffline(false)
    
    // Data should sync automatically
    await expect(page.locator('[data-testid="sync-queued"]')).not.toBeVisible({ timeout: 10000 })
  })

  test('should handle rapid offline/online switches @critical', async ({ page, context }) => {
    // Navigate to workout
    await page.getByRole('button', { name: /start workout|today's workout/i }).click()
    await page.waitForURL('/workout')
    
    // Rapidly switch between offline and online
    for (let i = 0; i < 5; i++) {
      await context.setOffline(true)
      await page.waitForTimeout(500)
      
      // Try an action while offline
      if (i === 2) {
        await page.locator('[data-testid="exercise-item"]').first().click()
        await page.locator('input[name="weight"]').fill(`${100 + i}`)
      }
      
      await context.setOffline(false)
      await page.waitForTimeout(500)
    }
    
    // App should still be functional
    await expect(page.locator('[data-testid="exercise-list"]')).toBeVisible()
    
    // No error messages should be visible
    await expect(page.locator('[role="alert"][data-type="error"]')).not.toBeVisible()
  })

  test('should show appropriate messages for offline actions @critical', async ({ page, context }) => {
    // Navigate to workout
    await page.getByRole('button', { name: /start workout|today's workout/i }).click()
    await page.waitForURL('/workout')
    
    // Go offline
    await context.setOffline(true)
    
    // Try different actions and verify messages
    
    // 1. Try to refresh data
    const refreshButton = page.locator('[data-testid="refresh-button"]')
    if (await refreshButton.isVisible()) {
      await refreshButton.click()
      await expect(page.locator('[data-testid="offline-refresh-message"]')).toBeVisible()
    }
    
    // 2. Try to complete workout
    const completeButton = page.getByRole('button', { name: /complete workout/i })
    if (await completeButton.isVisible()) {
      await completeButton.click()
      await expect(page.locator('[data-testid="offline-complete-message"]')).toBeVisible()
    }
    
    // Go back online
    await context.setOffline(false)
  })

  test('should handle service worker updates gracefully @critical', async ({ page }) => {
    // Check if service worker is registered
    const hasServiceWorker = await page.evaluate(() => 'serviceWorker' in navigator)
    
    if (hasServiceWorker) {
      // Wait for service worker to be ready
      await page.evaluate(() => navigator.serviceWorker.ready)
      
      // Check for update prompts
      const updatePrompt = page.locator('[data-testid="sw-update-prompt"]')
      if (await updatePrompt.isVisible({ timeout: 5000 })) {
        // Click update
        await page.getByRole('button', { name: /update|refresh/i }).click()
        
        // Page should reload
        await page.waitForLoadState('domcontentloaded')
        
        // App should still work
        await expect(page.locator('main')).toBeVisible()
      }
    }
  })
})