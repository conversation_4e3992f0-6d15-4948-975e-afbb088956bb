import { test, expect } from '@playwright/test'
import { setupRobustTestCleanup, setupPageWithRetry } from './helpers/robust-test-setup'

test.describe('Security Tests @critical', () => {
  // Use robust cleanup for all tests in this suite
  setupRobustTestCleanup()

  test.beforeEach(async ({ page }) => {
    await setupPageWithRetry(page, '/')
  })

  test.describe('XSS Protection', () => {
    test('should prevent XSS in toast notifications', async ({ page }) => {
      // Attempt XSS through toast
      await page.evaluate(() => {
        // @ts-ignore
        window.toast({
          title: '<script>alert("XSS")</script>',
          description: '<img src=x onerror=alert("XSS")>',
        })
      })

      // Check that script tags are not executed
      const alertCount = await page.evaluate(() => {
        return window.performance.getEntriesByType('resource').filter(
          entry => entry.name.includes('alert')
        ).length
      })
      expect(alertCount).toBe(0)

      // Check that content is properly escaped
      const toastContent = await page.textContent('#toast-container')
      expect(toastContent).toContain('<script>alert("XSS")</script>')
      expect(toastContent).toContain('<img src=x onerror=alert("XSS")>')
    })

    test('should prevent XSS in user inputs', async ({ page }) => {
      await page.goto('/login')

      // Try XSS in email field
      const emailInput = page.locator('input[type="email"]')
      await emailInput.fill('<script>alert("XSS")</script>@example.com')

      // Try XSS in password field
      const passwordInput = page.locator('input[type="password"]')
      await passwordInput.fill('<img src=x onerror=alert("XSS")>')

      // Submit form
      await page.locator('button[type="submit"]').click()

      // Verify no script execution
      const alertCount = await page.evaluate(() => {
        return window.performance.getEntriesByType('resource').filter(
          entry => entry.name.includes('alert')
        ).length
      })
      expect(alertCount).toBe(0)
    })

    test('should have Content Security Policy headers', async ({ page }) => {
      const response = await page.goto('/')
      const csp = response?.headers()['content-security-policy']

      expect(csp).toBeTruthy()
      expect(csp).toContain("default-src 'self'")
      expect(csp).toContain("object-src 'none'")
      expect(csp).toContain("base-uri 'self'")
      expect(csp).toContain('frame-ancestors \'none\'')
      expect(csp).toContain('block-all-mixed-content')
    })
  })

  test.describe('Authentication Security', () => {
    test('should not expose tokens in localStorage', async ({ page }) => {
      // Login first
      await page.goto('/login')
      await page.fill('input[type="email"]', '<EMAIL>')
      await page.fill('input[type="password"]', 'password123')
      await page.locator('button[type="submit"]').click()

      // Wait for potential token storage
      await page.waitForTimeout(1000)

      // Check localStorage for tokens
      const localStorageData = await page.evaluate(() => {
        const data: Record<string, any> = {}
        for (let i = 0; i < localStorage.length; i++) {
          const key = localStorage.key(i)
          if (key) {
            data[key] = localStorage.getItem(key)
          }
        }
        return data
      })

      // Verify no tokens in localStorage
      const stringifiedData = JSON.stringify(localStorageData)
      expect(stringifiedData).not.toContain('token')
      expect(stringifiedData).not.toContain('refreshToken')
      expect(stringifiedData).not.toContain('access_token')
    })

    test('should use httpOnly cookies for authentication', async ({ page, context }) => {
      // Login
      await page.goto('/login')
      await page.fill('input[type="email"]', '<EMAIL>')
      await page.fill('input[type="password"]', 'password123')
      await page.locator('button[type="submit"]').click()

      // Get cookies
      const cookies = await context.cookies()
      const authCookie = cookies.find(c => c.name === 'auth-token')
      const refreshCookie = cookies.find(c => c.name === 'refresh-token')

      // Verify httpOnly cookies exist
      if (authCookie) {
        expect(authCookie.httpOnly).toBe(true)
        expect(authCookie.sameSite).toBe('Strict')
      }

      if (refreshCookie) {
        expect(refreshCookie.httpOnly).toBe(true)
        expect(refreshCookie.sameSite).toBe('Strict')
      }
    })

    test('should implement proper session timeout', async ({ page }) => {
      // This is a placeholder for session timeout testing
      // In a real scenario, you'd test that sessions expire after inactivity
      expect(true).toBe(true)
    })
  })

  test.describe('CSRF Protection', () => {
    test('should include CSRF tokens in state-changing requests', async ({ page }) => {
      // Intercept API requests
      const apiRequests: any[] = []
      await page.route('**/api/**', async (route, request) => {
        if (['POST', 'PUT', 'DELETE', 'PATCH'].includes(request.method())) {
          apiRequests.push({
            url: request.url(),
            method: request.method(),
            headers: request.headers(),
          })
        }
        await route.continue()
      })

      // Perform a state-changing action (login)
      await page.goto('/login')
      await page.fill('input[type="email"]', '<EMAIL>')
      await page.fill('input[type="password"]', 'password123')
      await page.locator('button[type="submit"]').click()

      // Check if CSRF token is included
      const stateChangingRequests = apiRequests.filter(
        req => req.method !== 'GET'
      )

      // Note: CSRF implementation pending
      // expect(stateChangingRequests.some(req => req.headers['x-csrf-token'])).toBe(true)
    })
  })

  test.describe('Input Validation', () => {
    test('should validate email format', async ({ page }) => {
      await page.goto('/login')

      // Try invalid email formats
      const emailInput = page.locator('input[type="email"]')
      const submitButton = page.locator('button[type="submit"]')

      // Test various invalid formats
      const invalidEmails = [
        'notanemail',
        '@example.com',
        'user@',
        'user@.com',
        '<EMAIL>',
      ]

      for (const email of invalidEmails) {
        await emailInput.fill(email)
        await page.fill('input[type="password"]', 'password123')
        
        // Check HTML5 validation
        const isValid = await emailInput.evaluate((el: HTMLInputElement) => el.validity.valid)
        expect(isValid).toBe(false)
      }
    })

    test('should enforce password requirements', async ({ page }) => {
      await page.goto('/login')

      const passwordInput = page.locator('input[type="password"]')
      await page.fill('input[type="email"]', '<EMAIL>')

      // Test short password
      await passwordInput.fill('123')
      await page.locator('button[type="submit"]').click()

      // Should show error
      const error = await page.locator('text=/password/i').count()
      expect(error).toBeGreaterThan(0)
    })
  })

  test.describe('Security Headers', () => {
    test('should set all security headers', async ({ page }) => {
      const response = await page.goto('/')
      const headers = response?.headers() || {}

      // Check security headers
      expect(headers['x-content-type-options']).toBe('nosniff')
      expect(headers['x-frame-options']).toBe('DENY')
      expect(headers['x-xss-protection']).toBe('1; mode=block')
      expect(headers['referrer-policy']).toBe('strict-origin-when-cross-origin')
      expect(headers['strict-transport-security']).toContain('max-age=')
      expect(headers['permissions-policy']).toBeTruthy()

      // Verify dangerous headers are removed
      expect(headers['x-powered-by']).toBeUndefined()
    })
  })

  test.describe('Injection Attacks', () => {
    test('should prevent SQL injection in search', async ({ page }) => {
      // Navigate to a page with search functionality
      await page.goto('/program')

      // Try SQL injection patterns
      const injectionPatterns = [
        "'; DROP TABLE users; --",
        '" OR 1=1 --',
        '<script>alert("XSS")</script>',
        '${7*7}',
        '{{7*7}}',
      ]

      // If there's a search input, test it
      const searchInput = page.locator('input[type="search"], input[placeholder*="search" i]').first()
      if (await searchInput.count() > 0) {
        for (const pattern of injectionPatterns) {
          await searchInput.fill(pattern)
          await searchInput.press('Enter')

          // Verify no error or unexpected behavior
          await expect(page).not.toHaveURL(/error/i)
          const bodyText = await page.textContent('body')
          expect(bodyText).not.toContain('error')
          expect(bodyText).not.toContain('49') // 7*7
        }
      }
    })
  })

  test.describe('Data Exposure', () => {
    test('should not expose sensitive data in API responses', async ({ page }) => {
      // Intercept API responses
      await page.route('**/api/**', async (route, request) => {
        const response = await route.fetch()
        const body = await response.text()

        // Check for sensitive data patterns
        expect(body).not.toMatch(/password/i)
        expect(body).not.toMatch(/secret/i)
        expect(body).not.toMatch(/api[_-]?key/i)
        expect(body).not.toMatch(/private[_-]?key/i)

        await route.fulfill({ response })
      })

      // Navigate and trigger API calls
      await page.goto('/login')
      await page.fill('input[type="email"]', '<EMAIL>')
      await page.fill('input[type="password"]', 'password123')
      await page.locator('button[type="submit"]').click()
    })

    test('should not expose debug information in production', async ({ page }) => {
      // Check for debug commands
      const hasDebugCommands = await page.evaluate(() => {
        return 'drmuscle' in window
      })

      // In production, debug commands should not be available
      // This test would fail in development
      if (process.env.NODE_ENV === 'production') {
        expect(hasDebugCommands).toBe(false)
      }
    })
  })

  test.describe('Secure Communication', () => {
    test('should only load resources over HTTPS', async ({ page }) => {
      const insecureRequests: string[] = []

      // Monitor all requests
      page.on('request', request => {
        const url = request.url()
        if (url.startsWith('http://') && !url.includes('localhost')) {
          insecureRequests.push(url)
        }
      })

      await page.goto('/')
      await page.waitForLoadState('networkidle')

      // Verify no insecure requests
      expect(insecureRequests).toHaveLength(0)
    })
  })

  test.describe('Open Redirect', () => {
    test('should prevent open redirect vulnerabilities', async ({ page }) => {
      // Test various redirect patterns
      const redirectPatterns = [
        '//evil.com',
        'https://evil.com',
        'javascript:alert("XSS")',
        'data:text/html,<script>alert("XSS")</script>',
      ]

      for (const pattern of redirectPatterns) {
        await page.goto(`/?redirect=${encodeURIComponent(pattern)}`)
        
        // Verify we're still on the same origin
        const url = new URL(page.url())
        expect(url.origin).toBe('http://localhost:3000')
      }
    })
  })
})