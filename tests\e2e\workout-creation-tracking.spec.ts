/* eslint-disable no-console */
import { test, expect } from '@playwright/test'
import { setupRobustTestCleanup, setupPageWithRetry, waitForElementSafely, clickSafely } from './helpers/robust-test-setup'

test.describe('Workout Creation and Tracking @critical', () => {
  // Use robust cleanup for all tests in this suite
  setupRobustTestCleanup()

  const TEST_USER = {
    email: '<EMAIL>',
    password: 'Dr123456',
  }

  test.beforeEach(async ({ page }) => {
    // Login before each test with robust error handling
    try {
      await setupPageWithRetry(page, '/login')

      await waitForElementSafely(page, 'input[name="email"], [data-testid="email-input"]')
      await page.getByLabel('Email').fill(TEST_USER.email)
      await page.getByLabel('Password').fill(TEST_USER.password)

      await clickSafely(page, 'button[type="submit"], [data-testid="login-button"]')
      await page.waitForURL('/program', { timeout: 30000 })

      console.log('✅ [WORKOUT-CREATION-BEFOREEACH] Login completed successfully')
    } catch (error) {
      console.error('❌ [WORKOUT-CREATION-BEFOREEACH] Login failed:', error)
      throw error
    }
  })

  test('should display workout program overview @critical', async ({
    page,
  }) => {
    // Verify program page elements
    await expect(page.locator('h1')).toContainText(/program|workout/i)

    // Verify stats are displayed
    await expect(
      page.locator('[data-testid="workouts-completed"]')
    ).toBeVisible()
    await expect(page.locator('[data-testid="current-streak"]')).toBeVisible()
    await expect(page.locator('[data-testid="weight-lifted"]')).toBeVisible()

    // Verify workout button is visible
    const workoutButton = page.getByRole('button', {
      name: /start workout|today's workout/i,
    })
    await expect(workoutButton).toBeVisible()
  })

  test('should navigate to workout and display exercises @critical', async ({
    page,
  }) => {
    // Click workout button
    await page
      .getByRole('button', { name: /start workout|today's workout/i })
      .click()

    // Wait for workout page
    await page.waitForURL('/workout')
    await expect(page).toHaveURL('/workout')

    // Wait for exercises to load
    await expect(page.locator('[data-testid="exercise-list"]')).toBeVisible({
      timeout: 10000,
    })

    // Verify at least one exercise is displayed
    const exercises = page.locator('[data-testid="exercise-item"]')
    await expect(exercises.first()).toBeVisible()

    // Verify exercise has required elements
    const firstExercise = exercises.first()
    await expect(
      firstExercise.locator('[data-testid="exercise-name"]')
    ).toBeVisible()
    await expect(
      firstExercise.locator('[data-testid="sets-info"]')
    ).toBeVisible()
  })

  test('should track exercise completion @critical', async ({ page }) => {
    // Navigate to workout
    await page
      .getByRole('button', { name: /start workout|today's workout/i })
      .click()
    await page.waitForURL('/workout')

    // Click first exercise
    const firstExercise = page.locator('[data-testid="exercise-item"]').first()
    await firstExercise.click()

    // Wait for exercise detail page
    await expect(page.locator('[data-testid="set-input"]')).toBeVisible()

    // Complete a set
    const weightInput = page.locator('input[name="weight"]')
    const repsInput = page.locator('input[name="reps"]')

    // Fill in set data
    await weightInput.fill('100')
    await repsInput.fill('10')

    // Save set
    await page.getByRole('button', { name: /save|next|done/i }).click()

    // Should show rest timer or next set
    const restTimer = page.locator('[data-testid="rest-timer"]')
    const nextSetButton = page.getByRole('button', { name: /next set/i })

    await expect(restTimer.or(nextSetButton)).toBeVisible()
  })

  test('should handle RIR (Reps In Reserve) input @critical', async ({
    page,
  }) => {
    // Navigate to workout and exercise
    await page
      .getByRole('button', { name: /start workout|today's workout/i })
      .click()
    await page.waitForURL('/workout')
    await page.locator('[data-testid="exercise-item"]').first().click()

    // Complete a set
    await page.locator('input[name="weight"]').fill('100')
    await page.locator('input[name="reps"]').fill('10')
    await page.getByRole('button', { name: /save|next|done/i }).click()

    // Should show RIR selector
    await expect(page.locator('[data-testid="rir-selector"]')).toBeVisible()

    // Select RIR value
    await page.locator('[data-testid="rir-2"]').click()

    // Confirm RIR
    await page.getByRole('button', { name: /confirm|continue/i }).click()
  })

  test('should show rest timer between sets @critical', async ({ page }) => {
    // Navigate to exercise
    await page
      .getByRole('button', { name: /start workout|today's workout/i })
      .click()
    await page.waitForURL('/workout')
    await page.locator('[data-testid="exercise-item"]').first().click()

    // Complete a set
    await page.locator('input[name="weight"]').fill('100')
    await page.locator('input[name="reps"]').fill('10')
    await page.getByRole('button', { name: /save|next|done/i }).click()

    // If RIR appears, select it
    const rirSelector = page.locator('[data-testid="rir-selector"]')
    if (await rirSelector.isVisible({ timeout: 3000 })) {
      await page.locator('[data-testid="rir-2"]').click()
      await page.getByRole('button', { name: /confirm|continue/i }).click()
    }

    // Should show rest timer
    await expect(page.locator('[data-testid="rest-timer"]')).toBeVisible()

    // Timer should be counting down
    const initialTime = await page
      .locator('[data-testid="timer-display"]')
      .textContent()
    await page.waitForTimeout(2000)
    const currentTime = await page
      .locator('[data-testid="timer-display"]')
      .textContent()
    expect(initialTime).not.toBe(currentTime)
  })

  test('should complete full workout flow @critical', async ({ page }) => {
    // Navigate to workout
    await page
      .getByRole('button', { name: /start workout|today's workout/i })
      .click()
    await page.waitForURL('/workout')

    // Get exercises
    const exercises = page.locator('[data-testid="exercise-item"]')

    // Complete first exercise (simplified for test)
    await exercises.first().click()

    // Complete one set
    await page.locator('input[name="weight"]').fill('100')
    await page.locator('input[name="reps"]').fill('10')
    await page.getByRole('button', { name: /save|next|done/i }).click()

    // Skip rest timer if present
    const skipButton = page.getByRole('button', { name: /skip/i })
    if (await skipButton.isVisible({ timeout: 3000 })) {
      await skipButton.click()
    }

    // Mark exercise as complete
    const completeButton = page.getByRole('button', {
      name: /complete exercise|finish exercise|done/i,
    })
    if (await completeButton.isVisible()) {
      await completeButton.click()
    }

    // Should return to workout overview
    await expect(page.locator('[data-testid="exercise-list"]')).toBeVisible()

    // First exercise should show as completed
    const completedExercise = exercises.first()
    await expect(completedExercise).toHaveAttribute('data-completed', 'true')
  })

  test('should save workout progress @critical', async ({ page }) => {
    // Navigate to workout
    await page
      .getByRole('button', { name: /start workout|today's workout/i })
      .click()
    await page.waitForURL('/workout')

    // Complete a set
    await page.locator('[data-testid="exercise-item"]').first().click()
    await page.locator('input[name="weight"]').fill('100')
    await page.locator('input[name="reps"]').fill('10')

    // Intercept save request
    const savePromise = page.waitForResponse(
      (response) =>
        response.url().includes('/SaveWorkoutLog') && response.status() === 200
    )

    await page.getByRole('button', { name: /save|next|done/i }).click()

    // Verify save was successful
    const saveResponse = await savePromise
    expect(saveResponse.status()).toBe(200)
  })

  test('should handle network errors during set save @critical', async ({
    page,
  }) => {
    // Navigate to exercise
    await page
      .getByRole('button', { name: /start workout|today's workout/i })
      .click()
    await page.waitForURL('/workout')
    await page.locator('[data-testid="exercise-item"]').first().click()

    // Intercept save request and fail it
    await page.route('**/SaveWorkoutLog**', (route) => route.abort('failed'))

    // Try to save set
    await page.locator('input[name="weight"]').fill('100')
    await page.locator('input[name="reps"]').fill('10')
    await page.getByRole('button', { name: /save|next|done/i }).click()

    // Should show error message
    await expect(page.locator('[role="alert"]')).toBeVisible()
    await expect(page.locator('[role="alert"]')).toContainText(
      /error|failed|network/i
    )

    // Data should remain in form
    await expect(page.locator('input[name="weight"]')).toHaveValue('100')
    await expect(page.locator('input[name="reps"]')).toHaveValue('10')
  })

  test('should validate set inputs @critical', async ({ page }) => {
    // Navigate to exercise
    await page
      .getByRole('button', { name: /start workout|today's workout/i })
      .click()
    await page.waitForURL('/workout')
    await page.locator('[data-testid="exercise-item"]').first().click()

    // Try to save without filling inputs
    await page.getByRole('button', { name: /save|next|done/i }).click()

    // Should show validation errors
    const weightInput = page.locator('input[name="weight"]')
    const repsInput = page.locator('input[name="reps"]')

    await expect(weightInput).toHaveAttribute('aria-invalid', 'true')
    await expect(repsInput).toHaveAttribute('aria-invalid', 'true')
  })

  test('should show workout completion summary @critical', async ({ page }) => {
    // This would be a more complex test in real implementation
    // For now, verify the complete workout button exists
    await page
      .getByRole('button', { name: /start workout|today's workout/i })
      .click()
    await page.waitForURL('/workout')

    // Look for complete workout button
    const completeWorkoutButton = page.getByRole('button', {
      name: /complete workout|finish workout/i,
    })

    // If visible, click it
    if (await completeWorkoutButton.isVisible({ timeout: 5000 })) {
      await completeWorkoutButton.click()

      // Should show completion screen or redirect
      await expect(page).toHaveURL(/complete|summary|program/i)
    }
  })
})
