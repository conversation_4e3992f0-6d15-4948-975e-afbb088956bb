'use client'

import { useState, useEffect } from 'react'
import type { ExerciseModel, WorkoutTemplateModel } from '@/types'
import { getExercisesByBodyPart } from '@/api/exercise/search'
import {
  swapExerciseService,
  type SwapExerciseOptions,
  type ExerciseSwapResult,
} from '@/services/swapExerciseService'
import { logger } from '@/utils/logger'

interface ExerciseSwapModalProps {
  isOpen: boolean
  currentExercise: ExerciseModel
  workout: WorkoutTemplateModel
  onSwap: (result: ExerciseSwapResult) => void
  onClose: () => void
}

export function ExerciseSwapModal({
  isOpen,
  currentExercise,
  workout,
  onSwap,
  onClose,
}: ExerciseSwapModalProps) {
  const [alternatives, setAlternatives] = useState<ExerciseModel[]>([])
  const [selectedExercise, setSelectedExercise] =
    useState<ExerciseModel | null>(null)
  const [swapOption, setSwapOption] = useState<'temporary' | 'permanent'>(
    'temporary'
  )
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Load alternative exercises when modal opens
  useEffect(() => {
    if (!isOpen || !currentExercise.BodyPartId) return

    const loadAlternatives = async () => {
      setIsLoading(true)
      setError(null)

      try {
        const exercises = await getExercisesByBodyPart(
          currentExercise.BodyPartId!,
          currentExercise.Id
        )
        setAlternatives(exercises)
      } catch (err) {
        logger.error('[ExerciseSwapModal] Failed to load alternatives:', err)
        setError('Failed to load alternative exercises')
      } finally {
        setIsLoading(false)
      }
    }

    loadAlternatives()
  }, [isOpen, currentExercise.BodyPartId, currentExercise.Id])

  // Reset selection when modal opens/closes
  useEffect(() => {
    if (!isOpen) {
      setSelectedExercise(null)
      setSwapOption('temporary')
      setError(null)
    }
  }, [isOpen])

  const handleSwap = async () => {
    if (!selectedExercise) return

    setIsLoading(true)
    setError(null)

    try {
      const options: SwapExerciseOptions = {
        temporary: swapOption === 'temporary',
        permanent: swapOption === 'permanent',
      }

      const result = await swapExerciseService.swapExercise(
        workout,
        currentExercise.Id,
        selectedExercise,
        options
      )

      onSwap(result)
      onClose()
    } catch (err) {
      logger.error('[ExerciseSwapModal] Swap failed:', err)
      setError(err instanceof Error ? err.message : 'Failed to swap exercise')
    } finally {
      setIsLoading(false)
    }
  }

  if (!isOpen) return null

  return (
    <div
      className="fixed inset-0 z-[60] flex items-end justify-center bg-black bg-opacity-50 sm:items-center"
      onClick={onClose}
      onKeyDown={(e) => {
        if (e.key === 'Escape') onClose()
      }}
      role="presentation"
    >
      <div
        role="dialog"
        aria-label="Swap Exercise"
        className="w-full max-w-lg rounded-t-2xl bg-bg-primary p-6 sm:rounded-2xl shadow-theme-xl border border-brand-primary/10"
        onClick={(e) => e.stopPropagation()}
        onKeyDown={(e) => e.stopPropagation()}
      >
        <h2 className="mb-4 text-xl font-bold text-text-primary">
          Swap Exercise
        </h2>
        <p className="mb-4 text-sm text-text-secondary">
          Replace{' '}
          <strong className="text-text-primary">{currentExercise.Label}</strong>{' '}
          with:
        </p>

        {/* Loading State */}
        {isLoading && (
          <div className="mb-4 flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-brand-primary" />
            <span className="ml-3 text-text-secondary">
              Loading alternatives...
            </span>
          </div>
        )}

        {/* Error State */}
        {error && (
          <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-sm text-red-600">{error}</p>
            <button
              onClick={() => window.location.reload()}
              className="mt-2 text-sm text-red-700 hover:text-red-800 underline"
            >
              Try refreshing the page
            </button>
          </div>
        )}

        {/* Swap Options */}
        {!isLoading && !error && alternatives.length > 0 && (
          <div className="mb-4 p-4 bg-bg-secondary rounded-lg border border-border-primary">
            <h3 className="text-sm font-medium text-text-primary mb-3">
              Swap Type:
            </h3>
            <div className="space-y-2">
              <label className="flex items-center gap-3 cursor-pointer">
                <input
                  type="radio"
                  value="temporary"
                  checked={swapOption === 'temporary'}
                  onChange={(e) => setSwapOption(e.target.value as 'temporary')}
                  className="w-4 h-4 text-brand-primary border-border-primary focus:ring-brand-primary"
                />
                <div>
                  <span className="text-sm font-medium text-text-primary">
                    Swap for this workout only
                  </span>
                  <p className="text-xs text-text-secondary">
                    Changes will be saved locally and applied to this workout
                    session
                  </p>
                </div>
              </label>
              <label className="flex items-center gap-3 cursor-pointer">
                <input
                  type="radio"
                  value="permanent"
                  checked={swapOption === 'permanent'}
                  onChange={(e) => setSwapOption(e.target.value as 'permanent')}
                  className="w-4 h-4 text-brand-primary border-border-primary focus:ring-brand-primary"
                />
                <div>
                  <span className="text-sm font-medium text-text-primary">
                    Replace in all future workouts
                  </span>
                  <p className="text-xs text-text-secondary">
                    Changes will be saved to your workout template permanently
                  </p>
                </div>
              </label>
            </div>
          </div>
        )}

        {/* Exercise List */}
        {!isLoading && !error && (
          <div className="mb-4 max-h-64 space-y-2 overflow-y-auto">
            {alternatives.length === 0 ? (
              <div className="text-center py-8 text-text-secondary">
                <p>No alternative exercises found for this body part.</p>
                <p className="text-sm mt-2">
                  Try refreshing or contact support.
                </p>
              </div>
            ) : (
              alternatives.map((exercise) => (
                <button
                  key={exercise.Id}
                  onClick={() => setSelectedExercise(exercise)}
                  className={`w-full rounded-lg border p-3 text-left transition-all hover:bg-bg-secondary ${
                    selectedExercise?.Id === exercise.Id
                      ? 'border-brand-primary bg-brand-primary/5 shadow-theme-sm'
                      : 'border-border-primary hover:border-brand-primary/30'
                  }`}
                >
                  <h3 className="font-medium text-text-primary">
                    {exercise.Label}
                  </h3>
                  <div className="mt-1 flex items-center gap-2 text-xs text-text-secondary">
                    {exercise.RepsMinValue && exercise.RepsMaxValue && (
                      <span>
                        {exercise.RepsMinValue}-{exercise.RepsMaxValue} reps
                      </span>
                    )}
                    {exercise.IsBodyweight && (
                      <span className="px-2 py-0.5 bg-green-100 text-green-700 rounded">
                        Bodyweight
                      </span>
                    )}
                    {exercise.IsSystemExercise && (
                      <span className="px-2 py-0.5 bg-blue-100 text-blue-700 rounded">
                        System
                      </span>
                    )}
                  </div>
                </button>
              ))
            )}
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex gap-2">
          <button
            onClick={onClose}
            className="flex-1 rounded-lg border border-border-primary py-3 text-text-secondary hover:bg-bg-secondary transition-colors"
          >
            Cancel
          </button>
          <button
            onClick={handleSwap}
            disabled={!selectedExercise || isLoading}
            className="flex-1 rounded-lg bg-gradient-metallic-gold py-3 text-text-inverse font-semibold hover:shadow-theme-lg disabled:opacity-50 disabled:cursor-not-allowed transition-all"
          >
            {(() => {
              if (isLoading) return 'Saving...'
              if (swapOption === 'permanent') return 'Save Permanently'
              return 'Swap Exercise'
            })()}
          </button>
        </div>
      </div>
    </div>
  )
}
