# Consolidated Exercise Sets Page Plan

## Executive Summary

The exercise sets page has multiple implementation approaches documented. This consolidated guide merges all instructions into a coherent step-by-step plan to fix the "fucked" exercise page.

## Current State Analysis

### Conflicting Implementations

1. **Grid-based approach**: SetCell.tsx, ExerciseSetsGrid.tsx, SetScreenWithGrid.tsx
2. **List-based approach**: SetListMobile.tsx, useSetListMobile.ts
3. **Current components**: SetDisplay.tsx, AllSetsDisplay.tsx, HistoricalSetsDisplay.tsx

### Core Requirements

- Display all sets (warmups + work sets) at once
- Inline editing for reps and weight
- Visual states (completed, current, next)
- Mobile-optimized with 44px touch targets
- Exercise swap functionality
- Historical data display
- Set generation from recommendations

## Step-by-Step Implementation Plan

### Phase 1: Assessment and Cleanup

1. **Audit existing components**

   ```bash
   # Check which components actually exist
   ls -la src/components/workout/Set*.tsx
   ls -la src/hooks/useSet*.ts

   # Check current integration point
   grep -n "SetDisplay\|SetList\|SetGrid" src/components/workout/SetScreen.tsx
   ```

2. **Determine active implementation**
   - Check SetScreen.tsx to see which component is being used
   - Review git history to understand what was changed
   - Document findings in docs/status.md

3. **Run tests to identify failures**
   ```bash
   npm run test -- src/components/workout/**/*Set*.test.tsx
   npm run typecheck
   ```

### Phase 2: Set Generation and Data Flow

1. **Verify set generation logic**
   - Check `src/utils/createWorkoutSets.ts` exists and works
   - Ensure it handles all set types:
     - Warmup sets (from recommendation.WarmUpsList)
     - Normal work sets
     - Pyramid sets (weight ↑, reps ↓)
     - Reverse pyramid (weight ↓, reps ↑)
     - Back-off sets (20% lighter)
     - Rest-pause sets

2. **Validate data flow**

   ```
   API (RecommendationModel) → useSetScreenLogic → createWorkoutSets → UI Component
   ```

3. **Required fields for each set**
   ```typescript
   {
     SetNo: string,          // "W" for warmups, "1", "2" for work sets
     IsWarmups: boolean,
     IsNext: boolean,        // First unfinished set
     IsFinished: boolean,
     Weight: number,
     Reps: number,
     SetTitle?: string,      // "Pyramid set:", etc.
     LastTimeSet?: string,   // "Last time: X × Y kg"
     IsMaxSet?: boolean,
     IsBackOffSet?: boolean,
     NbPause?: number        // For rest-pause
   }
   ```

### Phase 3: UI Implementation Strategy

#### Option A: Grid-Based Approach (if SetCell exists)

1. **Component structure**:
   - SetCell.tsx - Individual grid cells
   - ExerciseSetsGrid.tsx - Grid container
   - SetScreenWithGrid.tsx - Integration wrapper

2. **Grid layout**:

   ```
   SET | REPS | * | LBS
   W   | 5    | × | 135
   1   | 10   | × | 225
   ```

3. **Features**:
   - Inline editing for reps/weight
   - Check/delete icons
   - "All sets done" message
   - "Finish exercise" button

#### Option B: List-Based Approach (if SetListMobile exists)

1. **Component structure**:
   - SetListMobile.tsx - Mobile-optimized list
   - useSetListMobile.ts - Data transformation hook

2. **List layout**:
   - Each set as a full-width row
   - Large touch targets
   - Clear visual hierarchy

#### Option C: Fix Current Implementation

1. **Enhance existing components**:
   - Update AllSetsDisplay.tsx for better mobile UX
   - Fix SetDisplay.tsx touch targets
   - Improve inline editing

### Phase 4: Feature Integration

1. **Exercise Swap Feature**
   - Integrate SwapExerciseService
   - Add swap button to exercise header
   - Support temporary and permanent swaps
   - Store temp swaps in localStorage

2. **Historical Data**
   - Display previous workout sets
   - Use recommendation.HistorySet data
   - Show "Last time: X × Y kg" format

3. **Set Completion Flow**
   - Mark sets as completed on tap
   - Auto-advance to next set
   - Show "All sets done—congrats!"
   - Enable "Finish exercise" button

### Phase 5: Testing and Verification

1. **Unit Tests**

   ```bash
   # Set generation
   npm run test -- src/utils/__tests__/createWorkoutSets.test.ts

   # UI components
   npm run test -- src/components/workout/__tests__/*Set*.test.tsx
   ```

2. **Integration Tests**
   - Test data flow from API to UI
   - Verify set updates persist
   - Check exercise completion

3. **Manual Testing Checklist**
   - [ ] All sets visible at once
   - [ ] Inline editing works for reps/weight
   - [ ] Touch targets ≥ 44px
   - [ ] Visual states clear (completed/current/next)
   - [ ] Exercise swap functional
   - [ ] Historical data displays
   - [ ] Weight units (kg/lbs) correct
   - [ ] Mobile viewport (320-430px) optimized

### Phase 6: Debug and Fix

1. **Common Issues**
   - Missing imports or incorrect paths
   - TypeScript type mismatches
   - Theme color inconsistencies (bg-brand-primary vs bg-primary)
   - Hook API changes
   - Missing mock data in tests

2. **Debug Commands**

   ```bash
   # Check for TypeScript errors
   npm run typecheck

   # Lint issues
   npm run lint

   # Bundle size
   npm run analyze

   # Git changes
   git diff src/components/workout/
   ```

3. **Browser Console Checks**
   ```javascript
   // Verify data structure
   console.log('currentExercise:', currentExercise)
   console.log('recommendation:', recommendation)
   console.log('exerciseWorkSets:', exerciseWorkSets)
   ```

### Phase 7: Performance and Polish

1. **Optimize for Mobile**
   - Lazy load heavy components
   - Minimize re-renders
   - Add loading states
   - Implement error boundaries

2. **Enhance UX**
   - Add haptic feedback
   - Smooth transitions
   - Clear success/error messages
   - Keyboard support for inputs

## Decision Matrix

Choose implementation based on audit results:

| If...                              | Then...                                   |
| ---------------------------------- | ----------------------------------------- |
| SetCell.tsx exists and tests pass  | Use Grid-Based approach                   |
| SetListMobile.tsx exists and works | Use List-Based approach                   |
| Both exist                         | Compare mobile performance, choose better |
| Neither exists properly            | Fix current AllSetsDisplay implementation |

## Success Criteria

- [ ] All sets display correctly
- [ ] Inline editing functional
- [ ] Touch targets ≥ 44px
- [ ] All tests pass
- [ ] No TypeScript errors
- [ ] Mobile performance < 1s load
- [ ] Exercise swap works
- [ ] Set generation accurate

## Next Steps

1. Run Phase 1 assessment commands
2. Document findings in docs/status.md
3. Choose implementation approach
4. Execute phases 2-7 systematically
5. Update this document with results

## References

- Original files reviewed:
  - exercise-page-layout-guide.md
  - exercise-swap-feature.md
  - set-generation-plan.md
  - sets-data-flow.md
  - sets-display.md
