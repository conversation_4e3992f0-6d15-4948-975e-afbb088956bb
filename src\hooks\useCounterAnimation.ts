import { useEffect, useState, useRef } from 'react'
import { trackMetricLoaded } from '@/utils/userInfoPerformance'

interface UseCounterAnimationProps {
  targetValue: number | null | undefined
  duration?: number
  label?: string
  showShimmer?: boolean
}

export function useCounterAnimation({
  targetValue,
  duration = 400,
  label,
  showShimmer = false,
}: UseCounterAnimationProps) {
  const [displayValue, setDisplayValue] = useState(0)
  const [hasAnimated, setHasAnimated] = useState(false)
  const [isTransitioning, setIsTransitioning] = useState(false)
  const [showGlow, setShowGlow] = useState(false)

  const animationRef = useRef<number | undefined>(undefined)
  const startTimeRef = useRef<number | undefined>(undefined)
  const startValueRef = useRef<number>(0)
  const hasStartedRef = useRef(false)

  // Check for reduced motion preference
  const prefersReducedMotion =
    typeof window !== 'undefined' &&
    window.matchMedia?.('(prefers-reduced-motion: reduce)').matches

  useEffect(() => {
    const target = targetValue ?? 0

    // Debug logging
    if (process.env.NODE_ENV === 'development' && label) {
      console.log(`[useCounterAnimation] ${label}: Effect triggered`, {
        target,
        displayValue,
        hasStarted: hasStartedRef.current,
        showShimmer,
        hasAnimated,
      })
    }

    // If this is the first time we're getting a real value and shimmer is showing
    if (!hasStartedRef.current && target > 0 && showShimmer) {
      if (process.env.NODE_ENV === 'development' && label) {
        console.log(
          `[useCounterAnimation] ${label}: Starting first animation from shimmer`
        )
      }
      hasStartedRef.current = true
      setIsTransitioning(true)

      // Add glow effect briefly during transition
      setTimeout(() => {
        setShowGlow(true)
        setTimeout(() => setShowGlow(false), 300)
      }, 100)
    }

    // If reduced motion is preferred, set value immediately
    if (prefersReducedMotion || duration === 0) {
      setDisplayValue(target)
      setIsTransitioning(false)
      return
    }

    // Cancel any ongoing animation
    if (animationRef.current) {
      if (process.env.NODE_ENV === 'development' && label) {
        console.log(
          `[useCounterAnimation] ${label}: Cancelling ongoing animation`
        )
      }
      cancelAnimationFrame(animationRef.current)
    }

    // Start animation
    if (process.env.NODE_ENV === 'development' && label) {
      console.log(`[useCounterAnimation] ${label}: Starting animation`, {
        from: displayValue,
        to: target,
        duration,
      })
    }
    startValueRef.current = displayValue
    startTimeRef.current = Date.now()

    const animate = () => {
      const now = Date.now()
      const elapsed = now - (startTimeRef.current || now)
      const progress = Math.min(elapsed / duration, 1)

      // Easing function (ease-out)
      const easeOut = 1 - Math.pow(1 - progress, 3)

      const currentValue = Math.round(
        startValueRef.current + (target - startValueRef.current) * easeOut
      )

      setDisplayValue(currentValue)

      if (progress < 1) {
        animationRef.current = requestAnimationFrame(animate)
      } else {
        if (process.env.NODE_ENV === 'development' && label) {
          console.log(`[useCounterAnimation] ${label}: Animation completed`, {
            finalValue: target,
            hasAnimated,
          })
        }
        setHasAnimated(true)
        setIsTransitioning(false)

        // Haptic feedback on mobile when animation completes
        if ('vibrate' in navigator && target > 0 && !hasAnimated) {
          navigator.vibrate(10)
        }

        // Track metric loaded for performance monitoring
        if (target > 0 && label && !hasAnimated) {
          if (
            label.toLowerCase().includes('week') ||
            label.toLowerCase().includes('streak')
          ) {
            trackMetricLoaded('streak')
          } else if (label.toLowerCase().includes('workout')) {
            trackMetricLoaded('workouts')
          } else if (
            label.toLowerCase().includes('lbs') ||
            label.toLowerCase().includes('lifted')
          ) {
            trackMetricLoaded('volume')
          }
        }
      }
    }

    animationRef.current = requestAnimationFrame(animate)

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current)
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [targetValue, duration, prefersReducedMotion, showShimmer, label])

  return {
    displayValue,
    hasAnimated,
    isTransitioning,
    showGlow,
    hasStarted: hasStartedRef.current,
  }
}
