// eslint-disable-next-line import/no-extraneous-dependencies
import { defineConfig, devices } from '@playwright/test'

/**
 * Optimized CI-specific Playwright configuration
 * with parallel execution and sharding support
 */
export default defineConfig({
  testDir: './tests/e2e',
  /* Disable parallel execution to prevent browser context conflicts */
  fullyParallel: false,
  /* Fail the build on CI if you accidentally left test.only in the source code. */
  forbidOnly: true,
  /* Retry on CI */
  retries: 5, // Increased retries for WebKit browser stability issues
  /* Use single worker on CI to prevent resource exhaustion on self-hosted runners */
  workers: 1, // Always use 1 worker to prevent browser crashes
  /* Support for sharding tests across multiple machines */
  /* Usage: --shard=1/4 --shard=2/4 etc. */
  /* Reporter configuration for CI */
  reporter: [
    ['html', { outputFolder: 'playwright-report' }],
    ['junit', { outputFile: 'test-results/junit.xml' }],
    ['list'],
    ['json', { outputFile: 'test-results/results.json' }],
  ],
  /* Timeout for each test */
  timeout: 120000, // Increase timeout for CI stability and browser launch issues
  /* Expect timeout for assertions */
  expect: {
    timeout: 30000, // Increase expect timeout for slow CI environments
  },
  /* Shared settings for all the projects below */
  use: {
    /* Base URL for local testing in CI */
    baseURL: 'http://localhost:3000',
    /* Collect trace when retrying the failed test */
    trace: 'on-first-retry',
    /* Screenshot on failure */
    screenshot: 'only-on-failure',
    /* Video on failure */
    video: 'retain-on-failure',
    /* Timeout for each action */
    actionTimeout: 20000, // Increase action timeout
    /* Navigation timeout */
    navigationTimeout: 30000, // Increase navigation timeout
    /* Browser launch options for CI stability */
    launchOptions: {
      timeout: 240000, // 240 seconds for browser launch (increased for stability)
      // Additional WebKit stability options
      args: [
        '--disable-web-security',
        '--disable-features=TranslateUI',
        '--disable-ipc-flooding-protection',
        '--disable-backgrounding-occluded-windows',
        '--disable-renderer-backgrounding',
        '--disable-background-timer-throttling',
        '--disable-background-networking',
        '--disable-features=VizDisplayCompositor',
      ],
      // Force single process for WebKit stability
      chromiumSandbox: false,
    },
    /* Context options for stability */
    contextOptions: {
      // Reduce memory usage
      viewport: { width: 390, height: 844 },
      // Disable unnecessary features
      javaScriptEnabled: true,
      bypassCSP: true,
      ignoreHTTPSErrors: true,
      // Reduce resource usage
      reducedMotion: 'reduce',
    },
    /* Global test setup */
    extraHTTPHeaders: {
      'X-Test-Context': 'playwright-ci',
    },
  },

  /* Global setup and teardown */
  globalSetup: require.resolve('./tests/e2e/global-setup.ts'),
  globalTeardown: require.resolve('./tests/e2e/global-teardown.ts'),

  /* Configure projects for critical mobile paths */
  projects: [
    /* Mobile Safari - Primary target for critical tests */
    {
      name: 'Mobile Safari',
      use: {
        ...devices['iPhone 13'],
        viewport: { width: 390, height: 844 },
        hasTouch: true,
        isMobile: true,
        // WebKit-specific options for stability
        launchOptions: {
          timeout: 300000, // 5 minutes timeout for WebKit stability
          slowMo: 250, // Add delay between actions for stability
          args: [
            '--disable-web-security',
            '--disable-features=TranslateUI',
            '--disable-ipc-flooding-protection',
            '--disable-backgrounding-occluded-windows',
            '--disable-renderer-backgrounding',
            '--disable-background-timer-throttling',
            '--disable-background-networking',
            '--disable-features=VizDisplayCompositor',
            '--no-sandbox',
            '--disable-setuid-sandbox',
            '--disable-dev-shm-usage',
          ],
          // Force single process for WebKit stability
          chromiumSandbox: false,
        },
        // Additional context options for WebKit
        contextOptions: {
          viewport: { width: 390, height: 844 },
          reducedMotion: 'reduce',
          // Disable animations for stability
          forcedColors: 'none',
        },
        // Increase timeouts for WebKit
        actionTimeout: 30000,
        navigationTimeout: 45000,
      },
      testMatch: /.*@critical.*\.spec\.ts$/,
      // Add test-level retries for WebKit
      retries: 4, // More retries for WebKit instability
    },
    /* Mobile Chrome - Run for critical tests only */
    {
      name: 'Mobile Chrome',
      use: {
        ...devices['Pixel 5'],
        viewport: { width: 393, height: 851 },
        hasTouch: true,
        isMobile: true,
        // Chrome-specific options for stability
        launchOptions: {
          timeout: 120000,
          args: [
            '--disable-dev-shm-usage',
            '--no-sandbox',
            '--disable-setuid-sandbox',
            '--disable-gpu',
          ],
        },
      },
      testMatch: /.*@critical.*\.spec\.ts$/,
    },
    /* Mobile Safari - All tests */
    {
      name: 'Mobile Safari Full',
      use: {
        ...devices['iPhone 13'],
        viewport: { width: 390, height: 844 },
        hasTouch: true,
        isMobile: true,
        // WebKit-specific options for stability
        launchOptions: {
          timeout: 300000, // 5 minutes timeout for WebKit stability
          slowMo: 250, // Add delay between actions for stability
          args: [
            '--disable-web-security',
            '--disable-features=TranslateUI',
            '--disable-ipc-flooding-protection',
            '--disable-backgrounding-occluded-windows',
            '--disable-renderer-backgrounding',
            '--disable-background-timer-throttling',
            '--disable-background-networking',
            '--disable-features=VizDisplayCompositor',
            '--no-sandbox',
            '--disable-setuid-sandbox',
            '--disable-dev-shm-usage',
          ],
          // Force single process for WebKit stability
          chromiumSandbox: false,
        },
        // Additional context options for WebKit
        contextOptions: {
          viewport: { width: 390, height: 844 },
          reducedMotion: 'reduce',
          // Disable animations for stability
          forcedColors: 'none',
        },
        // Increase timeouts for WebKit
        actionTimeout: 30000,
        navigationTimeout: 45000,
      },
      testIgnore: /.*@critical.*\.spec\.ts$/,
      // Add test-level retries for WebKit
      retries: 4, // More retries for WebKit instability
    },
  ],

  /* Run local dev server before tests */
  webServer: {
    command: 'npm run start',
    port: 3000,
    reuseExistingServer: !process.env.CI,
    timeout: 120000,
  },
})
